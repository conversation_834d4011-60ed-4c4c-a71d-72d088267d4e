import request from '@/config/axios'

// 库存批次信息 VO
export interface BatchInfoVO {
  id: number // 批次ID
  inventoryId: number // 库存编号
  materialId: number // 物料ID
  materialName: string // 物料名称
  materialCode: string // 物料编码
  materialType: string // 物料类型
  materialSource: string // 物料来源
  batchNo: string // 批号
  spec: string // 规格
  specQuantity: string // 规格数量
  quantity: number // 数量
  lockQuantity: number // 锁定数量
  unlockQuantity: number // 未锁数量
  quantityUnit: string // 数量单位
  auxiliaryUnit: string // 基本单位
  auxiliaryQuantity: number // 基本单位数量
  inventoryQuantity: number // 库存数量
  price: number // 价格
  priceUnit: string // 价格单位
  inDate: Date // 入库日期
  warehouseId: number // 仓库ID
  warehouseName: string // 仓库名称
  locationId: number // 仓位ID
  locationName: string // 仓位名称
  locations: string // 预留的多仓位
  status: string // 库存状态
  totalCost: number // 总价值
  remark: string // 备注
  tenantName: string // 租户名称
  purchasePrice: number // 采购价格
  salePrice: number // 销售价格
  objectId: number // 交易对象ID
  objectName: string // 交易对象名称
}

// 库存批次信息 API
export const BatchInfoApi = {
  // 查询库存批次信息分页
  getBatchInfoPage: async (params: any) => {
    return await request.get({ url: `/scm/inventory/batch-info/page`, params })
  },

  // 查询库存批次信息详情
  getBatchInfo: async (id: number) => {
    return await request.get({ url: `/scm/inventory/batch-info/get?id=` + id })

  },

  // 新增库存批次信息
  createBatchInfo: async (data: BatchInfoVO) => {
    return await request.post({ url: `/scm/inventory/batch-info/create`, data })
  },

  // 修改库存批次信息
  updateBatchInfo: async (data: BatchInfoVO) => {
    return await request.put({ url: `/scm/inventory/batch-info/update`, data })

  },

  // 删除库存批次信息
  deleteBatchInfo: async (id: number) => {
    return await request.delete({ url: `/scm/inventory/batch-info/delete?id=` + id })
  },

  // 导出库存批次信息 Excel
  exportBatchInfo: async (params) => {
    return await request.download({ url: `/scm/inventory/batch-info/export-excel`, params })
  },

  //通过物料id获取简单信息分页
  getSimpleBatchInfoListByMaterialIds: async (params) => {
    return await request.get({url: `/scm/inventory/batch-info/get-by-material-id`,params})
  }
}
