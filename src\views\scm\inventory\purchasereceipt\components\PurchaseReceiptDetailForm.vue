<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" border show-summary :summary-method="summaryMethod">
      <!-- <el-table-column label="序号" type="index" width="100" /> -->
      <el-table-column label="序号" min-width="60" prop="num" align="center"/>
       <!-- <el-table-column label="单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.bizOrderNo`" :rules="formRules.bizOrderNo" class="mb-0px!">
            <el-input v-model="row.bizOrderNo" placeholder="请输入单号" disabled/>
          </el-form-item>
        </template>
      </el-table-column>  -->
      <!-- <el-table-column label="物料ID" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialId`" :rules="formRules.materialId" class="mb-0px!">
            <el-input v-model="row.materialId" placeholder="请输入物料ID" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料名称" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialName`" :rules="formRules.materialName" class="mb-0px!">
            <el-input v-model="row.materialName" placeholder="请输入物料名称" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="物料编号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.materialCode`" :rules="formRules.materialCode" class="mb-0px!">
            <el-input v-model="row.materialCode" placeholder="请输入物料编号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="物料信息" min-width="280">
        <template #default="{ row }">
          <div class="material-spec-cell">
            <!-- 物料信息 -->
            <div class="info-row">
              <span class="info-label">物料:</span>
              <span class="info-value">{{ formatMaterialDisplay(row) }}</span>
            </div>
            <!-- 物料规格 -->
            <div class="info-row">
              <span class="info-label">规格:</span>
              <span class="info-value">{{ row.materialSpec || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="仓库" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.warehouseId`" :rules="formRules.warehouseId" class="mb-0px!">
            <el-tree-select
              v-model="row.warehouseId"
              :data="warehouseTreeData"
              placeholder="请选择仓库"
              clearable
              filterable
              check-strictly
              :render-after-expand="false"
              class="!w-130px"
              node-key="id"
              :props="{
                value: 'id',
                label: 'name',
                children: 'children',
                disabled: 'disabled'
              }"
              @change="(value) => handleWarehouseChange($index, value)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-select
              v-model="row.unit"
              placeholder="请选择单位"
              clearable
              class="!w-130px"
              disabled
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单价" min-width="120" prop="unitPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">
            <el-input
              v-model="row.unitPrice"
              placeholder="请输入单价"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="金额" min-width="150" prop="amount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.amount`" :rules="formRules.amount" class="mb-0px!">
            <el-input
              v-model="row.amount"
              placeholder="请输入金额"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="应收数量" min-width="120" prop="plannedQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.plannedQuantity`" :rules="formRules.plannedQuantity" class="mb-0px!">
            <el-input
              v-model="row.plannedQuantity"
              placeholder="来自采购订单"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="实收数量" min-width="100" prop="fulfilledQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.fulfilledQuantity`" :rules="formRules.fulfilledQuantity" class="mb-0px!">
            <el-input
              v-model="row.fulfilledQuantity"
              placeholder="请输入实收数量"
            />
          </el-form-item>
        </template>
      </el-table-column>
<!--      <el-table-column label="基本单位应收数量" min-width="140" prop="standardPlannedQuantity">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.standardPlannedQuantity`" :rules="formRules.standardPlannedQuantity" class="mb-0px!">-->
<!--            <el-input-->
<!--              v-model="row.standardPlannedQuantity"-->
<!--              placeholder="请输入基本单位应收数量"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="基本单位实收数量" min-width="140" prop="standardFulfilledQuantity">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.standardFulfilledQuantity`" :rules="formRules.standardFulfilledQuantity" class="mb-0px!">-->
<!--            <el-input-->
<!--              v-model="row.standardFulfilledQuantity"-->
<!--              placeholder="请输入基本单位实收数量"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
<!--      <el-table-column label="基本单位" min-width="120">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.standardUnit`" :rules="formRules.standardUnit" class="mb-0px!">-->
<!--            <el-select-->
<!--              v-model="row.standardUnit"-->
<!--              placeholder="请选择基本单位"-->
<!--              clearable-->
<!--              class="!w-130px"-->
<!--            >-->
<!--              <el-option-->
<!--                v-for="unit in unitList"-->
<!--                :key="unit.id"-->
<!--                :label="unit.name"-->
<!--                :value="unit.id"-->
<!--              />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <!-- <el-table-column label="含税单价" min-width="120" prop="taxPrice">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxPrice`" :rules="formRules.taxPrice" class="mb-0px!">
            <el-input
              v-model="row.taxPrice"
              placeholder="自动计算"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税金额" min-width="120" prop="taxAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxAmount`" :rules="formRules.taxAmount" class="mb-0px!">
            <el-input
              v-model="row.taxAmount"
              placeholder="自动计算"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票数量" min-width="150" prop="invoiceQuantity">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceQuantity`" :rules="formRules.invoiceQuantity" class="mb-0px!">
            <el-input
              v-model="row.invoiceQuantity"
              placeholder="开票时填写"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="开票金额" min-width="150" prop="invoiceAmount">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.invoiceAmount`" :rules="formRules.invoiceAmount" class="mb-0px!">
            <el-input
              v-model="row.invoiceAmount"
              placeholder="开票时填写"
              disabled
            />
          </el-form-item>
        </template>
      </el-table-column> -->
<!--      <el-table-column label="开票基本数量" min-width="150" prop="standardInvoiceQuantity">-->
<!--        <template #default="{ row, $index }">-->
<!--          <el-form-item :prop="`${$index}.standardInvoiceQuantity`" :rules="formRules.standardInvoiceQuantity" class="mb-0px!">-->
<!--            <el-input-->
<!--              v-model="row.standardInvoiceQuantity"-->
<!--              placeholder="请输入开票基本数量"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="生产日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.effictiveDate`" :rules="formRules.effictiveDate" class="mb-0px!">
            <el-date-picker
              v-model="row.effictiveDate"
              type="date"
              value-format="x"
              placeholder="选择生产日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="失效日期" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.expiryDate`" :rules="formRules.expiryDate" class="mb-0px!">
            <el-date-picker
              v-model="row.expiryDate"
              type="date"
              value-format="x"
              placeholder="选择失效日期"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="说明" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.note`" :rules="formRules.note" class="mb-0px!">
            <el-input v-model="row.note" placeholder="请输入说明" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- <el-table-column label="源单单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.sourceNo`" :rules="formRules.sourceNo" class="mb-0px!">
            <el-input v-model="row.sourceNo" placeholder="请输入源单单号" />
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column label="批号" min-width="200">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">
            <el-select
              v-model="row.batchNo"
              placeholder="请选择批号"
              clearable
              filterable
              class="!w-180px"
              :disabled="!row.materialId"
            >
              <el-option
                v-for="batch in row.batchOptions || []"
                :key="batch.id"
                :label="formatBatchLabel(batch)"
                :value="batch.batchNo"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="库位" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.locationId`" :rules="formRules.locationId" class="mb-0px!">
            <el-select
              v-model="row.locationId"
              placeholder="请选择库位"
              clearable
              filterable
              class="!w-130px"
            >
              <el-option
                v-for="location in getFilteredLocationList(row.warehouseId)"
                :key="location.id"
                :label="location.name"
                :value="location.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <!-- <el-button @click="handleDelete($index)" link>—</el-button> -->
           <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加采购入库明细</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { PurchaseReceiptApi } from '@/api/scm/inventory/purchasereceipt'
import { MaterialApi, MaterialVO } from '@/api/scm/base/material';
import { UnitApi,UnitVO } from '@/api/scm/base/unit';
import { WarehouseLocationApi,WarehouseLocationVO } from '@/api/scm/inventory/warehouselocation';
import { WarehouseApi, WarehouseVO } from '@/api/scm/inventory/warehouse';
import { BatchInfoApi } from '@/api/scm/inventory/batchinfo'
import { InventoryDetail } from '@/types/inventory';
import { getRemoteUnit } from '@/utils/commonBiz';
import { formatAmount, formatQuantity } from '@/utils/formatter';
import { handleTree } from '@/utils/tree';



const props = defineProps({
  bizOrderId: {
    type: [String, Number],
    default: undefined
  },
  warehouseId: {
    type: [String, Number],
    default: undefined
  },
  bizOrderNo: {
    type: String,
    default: undefined
  }
})

const formLoading = ref(false) // 表单的加载中
const formData = ref<InventoryDetail[]>([])
const formRules = reactive<any>({
})
const formRef = ref() // 表单 Ref
const locationList = ref<WarehouseLocationVO[]>([])
const warehouseList = ref<WarehouseVO[]>([]) // 仓库列表数据
const warehouseTreeData = ref<any[]>([]) // 仓库树形数据
const unitList = ref<any[]>([]) // 单位列表数据
/** 获取单位列表 */
const loadUnits = async () => {
  try {
    unitList.value = await getRemoteUnit()
  } catch (error) {
    console.error('获取单位数据失败:', error)
    unitList.value = []
  }
}

/** 加载批次选项 */
const loadBatchOptions = async (row: any, materialId: string | number) => {
  if (!materialId) {
    row.batchOptions = []
    row.batchNo = undefined
    return
  }

  try {
    const response = await BatchInfoApi.getSimpleBatchInfoListByMaterialId({
      materialId: Number(materialId)
    })
    
    const batchList = response || []
    row.batchOptions = batchList
    
    // 默认选中第一个批次
    if (batchList.length > 0) {
      row.batchNo = batchList[0].batchNo
    } else {
      row.batchNo = undefined
    }
  } catch (error) {
    row.batchOptions = []
    row.batchNo = undefined
  }
}

/** 格式化批次显示标签 */
const formatBatchLabel = (batch: any) => {
  if (!batch) return ''
  const batchNo = batch.batchNo || '无批号'
  return `${batchNo} (数量:${batch.quantity}, 未锁数量:${batch.unlockQuantity})`
}

onMounted(async () => {
  await initLocationList()
  await initWarehouseList()
  await loadUnits()
  
  // 如果初始化时已有物料数据，加载对应的批次数据
  if (formData.value && formData.value.length > 0) {
    for (const row of formData.value) {
      if (row.materialId && !row.batchOptions) {
        row.batchOptions = []
        await loadBatchOptions(row, row.materialId)
      }
    }
  }
})

/** 格式化物料显示信息 */
const formatMaterialDisplay = (row: any) => {
  if (!row) return '-'
  const code = row.materialCode || ''
  const name = row.materialName || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (code) parts.push(code)
  if (name) parts.push(name)

  return parts.length > 0 ? parts.join(' - ') : '-'
}

//库位信息相关方法
const initLocationList = async () => {
  try {
    // 先获取第一页数据，了解总数
    const firstPage = await WarehouseLocationApi.getWarehouseLocationPage({
      pageNo: 1,
      pageSize: 100
    })

    let allLocations = firstPage.list || []
    const total = firstPage.total || allLocations.length

    // 如果总数超过100，需要分页获取所有数据
    if (total > 100) {
      const totalPages = Math.ceil(total / 100)
      const promises = []

      for (let page = 2; page <= totalPages; page++) {
        promises.push(
          WarehouseLocationApi.getWarehouseLocationPage({
            pageNo: page,
            pageSize: 100
          })
        )
      }

      const results = await Promise.all(promises)
      results.forEach(res => {
        allLocations = allLocations.concat(res.list || [])
      })
    }

    locationList.value = allLocations
  } catch (error) {
    console.error('获取库位数据失败:', error)
    locationList.value = []
  }
}

// 仓库信息相关方法
const initWarehouseList = async () => {
  try {
    // 先获取第一页数据，了解总数
    const firstPage = await WarehouseApi.getWarehouseList({
      pageNo: 1,
      pageSize: 100
    })

    let allWarehouses = firstPage.list || firstPage || []
    const total = firstPage.total || allWarehouses.length

    // 如果总数超过100，需要分页获取所有数据
    if (total > 100) {
      const totalPages = Math.ceil(total / 100)
      const promises = []

      for (let page = 2; page <= totalPages; page++) {
        promises.push(
          WarehouseApi.getWarehouseList({
            pageNo: page,
            pageSize: 100
          })
        )
      }

      const results = await Promise.all(promises)
      results.forEach(res => {
        const pageData = res.list || res || []
        allWarehouses = allWarehouses.concat(pageData)
      })
    }

    warehouseList.value = allWarehouses
    // 转换为树形结构
    warehouseTreeData.value = handleTree(warehouseList.value, 'id', 'parentId')
  } catch (error) {
    console.error('获取仓库数据失败:', error)
    warehouseList.value = []
    warehouseTreeData.value = []
  }
}

// 根据仓库ID过滤库位列表
const getFilteredLocationList = (warehouseId: any) => {
  if (!warehouseId) return locationList.value
  return locationList.value.filter(location => location.warehouseId === warehouseId)
}

// 处理仓库选择变化
const handleWarehouseChange = (index: number, warehouseId: any) => {
  // 清空库位选择
  formData.value[index].locationId = undefined

  // 如果主表没有选择仓库，则更新主表仓库
  if (warehouseId && !props.warehouseId) {
    // 这里可以触发事件通知父组件更新主表仓库
    // emit('warehouse-change', warehouseId)
  }
}
/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.bizOrderId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true
      const details = await PurchaseReceiptApi.getPurchaseReceiptDetailListByBizOrderId(val)
      
      // 为每行添加批次选项字段并加载批次数据
      for (const row of details) {
        row.batchOptions = []
        if (row.materialId) {
          await loadBatchOptions(row, row.materialId)
        }
      }
      
      formData.value = details
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)
/** 根据物料类型获取默认仓库ID */
const getDefaultWarehouseByMaterial = (materialCode: string, materialType: string) => {
  // 物料编码以4开头，或物料类型为4，则为包装材料
  if (materialCode?.startsWith('4') || materialType === '4') {
    return 6260 // 包装材料仓库
  }
  return 6256 // 原材料仓库
}

watch(() => props.warehouseId,(val) => {
  if(!val) return
  formData.value.forEach(item => {
    // 如果子表行没有选择仓库，则根据物料类型设置默认仓库
    if (!item.warehouseId) {
      // 如果有物料信息，根据物料类型设置仓库，否则使用主表传入的仓库
      if (item.materialCode || item.materialType) {
        item.warehouseId = getDefaultWarehouseByMaterial(item.materialCode, item.materialType)
      } else {
        item.warehouseId = val
      }
      // 清空库位选择，因为仓库变了
      item.locationId = undefined
    }
  })
},
{ immediate: true }
)

watch(() => props.bizOrderNo,(val) => {
  formData.value.forEach(item => {
    item.bizOrderNo = val
  })
},
{ immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  // 自动生成序号：当前列表长度 + 1
  const nextNum = formData.value.length + 1

  const row: InventoryDetail = {
    id: undefined,
    num: nextNum.toString(),
    bizOrderId: props.bizOrderId,
    bizOrderNo: props.bizOrderNo,
    warehouseId: props.warehouseId || 6256, // 默认选中主表的仓库，如果没有则默认原材料仓库
    locationId: undefined,
    materialId: undefined,
    materialName: undefined,
    materialCode: undefined,
    materialSpec: undefined,
    unit: undefined,
    unitPrice: undefined,
    amount: undefined,
    remark: undefined,
    plannedQuantity: undefined,
    fulfilledQuantity: undefined,
    standardPlannedQuantity: undefined,
    standardFulfilledQuantity: undefined,
    standardUnit: undefined,
    taxPrice: undefined,
    taxAmount: undefined,
    invoiceQuantity: undefined,
    invoiceAmount: undefined,
    standardInvoiceQuantity: undefined,
    effictiveDate: undefined,
    expiryDate: undefined,
    note: undefined,
    sourceId: undefined,
    sourceNo: undefined,
    batchNo: undefined,
    batchOptions: [], // 添加批次选项
    costObjectId: undefined,
    costObjectName: undefined,
    accountingVoucherNumber: undefined,
  }
  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
  // 删除后重新排序序号
  reorderSequenceNumbers()
}

/** 重新排序序号 */
const reorderSequenceNumbers = () => {
  formData.value.forEach((item, index) => {
    item.num = (index + 1).toString()
  })
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  // 过滤掉batchOptions字段，避免传递给后端
  return formData.value.map(item => {
    const { batchOptions, ...rest } = item
    return rest
  })
}

/** 设置表单数据 */
const setData = async (data: InventoryDetail[]) => {
  if (!data || data.length === 0) {
    formData.value = []
    return
  }

  // 确保单位数据已加载
  if (unitList.value.length === 0) {
    await loadUnits()
  }

  // 处理每一行数据，转换单位信息和设置仓库
  const processedData = await Promise.all(data.map(async (item) => {
    const processedItem = { ...item }

    // 如果 unit 字段存在且是数字（单位ID），需要确保选择器能正确显示
    if (processedItem.unit) {
      const unitId = typeof processedItem.unit === 'string' ? parseInt(processedItem.unit) : processedItem.unit

      // 在单位列表中查找对应的单位
      const foundUnit = unitList.value.find(u => u.id === unitId)
      if (foundUnit) {
        processedItem.unit = foundUnit.id // 确保是数字ID
        processedItem.unitName = foundUnit.name
      } else {
        // 如果在本地列表中找不到，尝试从API获取
        try {
          const unitInfo = await UnitApi.getUnit(unitId)
          if (unitInfo) {
            processedItem.unit = unitInfo.id
            processedItem.unitName = unitInfo.name
          }
        } catch (error) {
          console.error(`获取单位信息失败:`, error)
        }
      }
    }

    // 如果没有设置仓库，根据物料类型设置默认仓库
    if (!processedItem.warehouseId && (processedItem.materialCode || processedItem.materialType)) {
      const materialCode = processedItem.materialCode || ''
      const materialType = processedItem.materialType || processedItem.type || ''
      processedItem.warehouseId = getDefaultWarehouseByMaterial(materialCode, materialType)
    } else if (!processedItem.warehouseId) {
      // 如果没有物料信息，使用主表传入的仓库或默认原材料仓库
      processedItem.warehouseId = props.warehouseId || 6256
    }

    // 初始化批次选项并加载批次数据
    processedItem.batchOptions = []
    if (processedItem.materialId) {
      await loadBatchOptions(processedItem, processedItem.materialId)
    }

    return processedItem
  }))

  formData.value = processedData
}

/** 表格汇总方法 */
const summaryMethod = ({ columns, data }: { columns: any[], data: any[] }) => {
  const sums: any[] = []

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    // 需要汇总的数量字段
    const quantityFields = ['plannedQuantity', 'fulfilledQuantity', 'standardPlannedQuantity',
                           'standardFulfilledQuantity', 'invoiceQuantity', 'standardInvoiceQuantity']

    // 需要汇总的金额字段
    const amountFields = ['unitPrice', 'amount', 'taxPrice', 'taxAmount', 'invoiceAmount']

    if (quantityFields.includes(column.property)) {
      // 数量字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatQuantity(total)
    } else if (amountFields.includes(column.property)) {
      // 金额字段汇总
      const values = data.map(item => Number(item[column.property]) || 0)
      const total = values.reduce((prev, curr) => prev + curr, 0)
      sums[index] = formatAmount(total)
    } else {
      // 其他字段不汇总
      sums[index] = ''
    }
  })

  return sums
}

defineExpose({ validate, getData, setData })
</script>

<style scoped>
.material-spec-cell {
  padding: 4px 0;
  line-height: 1.4;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 50px;
  flex-shrink: 0;
}

.info-value {
  color: #303133;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
