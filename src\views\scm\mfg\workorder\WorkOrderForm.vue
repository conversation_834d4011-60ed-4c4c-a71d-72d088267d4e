<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%" height="100%" :fullscreen="true">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      :inline="true"
    >
      <el-form-item label="生产单号" prop="workNo">
        <el-input v-model="formData.workNo" placeholder="保存时自动生成" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="来源类型" prop="orderType">
        <el-select v-model="formData.orderType" placeholder="请选择来源类型" class="!w-240px">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MFG_ORDER_SOURCE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="销售单号" prop="orderNo">
        <ScrollSelect
              filterable
              clearable
              class="!w-240px"
              v-model="formData.orderNo"
              placeholder="请选择销售订单"
              :load-method="getRemoteSaleOrder"
              :label-key="(item) => `${item.orderNo} - ${item.productName} - ${item.customerName}`"
              value-key="id"
              :default-value="{
                value: formData.orderDetailId,
                label: (formData.orderNo || '') + ' - ' + (formData.productName || '') + ' - ' + (formData.customerName || '')
              }"
              :query-key="formData.productName"
              :extra-params="{ 'productionStatus': 'not_planned'}"
              @change="(val, material) => {
                formData.orderDetailId = val
                formData.orderId = material?.orderId
                formData.orderNo = material?.orderNo
                formData.productId = material?.productId
                formData.productName = material?.productName
                formData.productCode = material?.productCode
                formData.productUnit = material?.unit
                formData.spec = material?.spec
                formData.productSubType = material?.subType
                formData.customerId = material?.customerId
                formData.customerName = material?.customerName
                formData.orderUnit = material?.unit
                formData.orderQuantity = material?.quantity
                formData.orderDate = material?.orderDate
                formData.deliverDate = material?.deliverDate
                formData.scheduleQuantity = material?.quantity
                formData.schedulePiece = material?.specQuantityTotal
                formData.bomLabel = null// 清空Bom编码
                formData.bomId = undefined
                formData.bomCode = undefined
                productDisabled = !!val // 当有订单ID时禁用产品选择
                getRemoteBom(material?.productId)
              }"
            />
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
         <ScrollSelect
              filterable
              clearable
              class="!w-240px"
              v-model="formData.productName"
              placeholder="请选择产品名称"
              :load-method="getRemoteMaterial"
              :label-key="(item) => `${item.name} - ${item.fullCode} - ${item.spec}`"
              :disabled="productDisabled === true"
              value-key="id"
              :default-value="{ 
                value: formData.productName, 
                label: formData.productName
              }"
              :query-key="productName"
              :extra-params="{ 'types': ([2,3])}"
              @change="(val, material) => {
                formData.productId = material.id
                formData.productName = material?.name
                formData.productCode = material?.fullCode
                formData.productSubType = material?.subType
                formData.productUnit = Number(material?.unit)
                formData.spec = material?.spec
                formData.orderUnit = material?.mfgUnit
                getRemoteBom(material?.id)
              }"
            />
      </el-form-item>
      <el-form-item label="订单数量" prop="orderQuantity">
        <el-input v-model="formData.orderQuantity" placeholder="请输入订单数量" class="!w-240px" disabled>
          <template #append>
            <el-select v-model="formData.productUnit" placeholder="请选择产品单位" class="!w-100px" disabled>
            <el-option
              v-for="dict in unitList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value" 
            />
          </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="规格" prop="spec">
        <el-input v-model="formData.spec" placeholder="请输入规格" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="客户" prop="customerName">
        <el-input v-model="formData.customerName" placeholder="请输入客户名称" class="!w-240px" disabled/>
      </el-form-item>
       
      <el-form-item label="下单时间" prop="orderDate">
        <el-date-picker
          v-model="formData.orderDate"
          type="date"
          value-format="x"
          placeholder="选择下单时间" class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="交期" prop="deliverDate">
        <el-date-picker
          v-model="formData.deliverDate"
          type="date"
          value-format="x"
          placeholder="选择交期" class="!w-240px"
        />
      </el-form-item>
          <el-form-item label="计划日期" prop="scheduleTimeRange">
            <el-date-picker
              v-model="formData.scheduleTimeRange"
              type="datetimerange"
              value-format="x"
              range-separator="至"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              class="!w-full"
              @change="calculateScheduleCostTime"
            />
          </el-form-item>
          <el-form-item label="计划用时" prop="scheduleCostTime">
            <div class="duration-input-group">
              <el-input
                v-model="scheduleDurationHoursDisplay"
                placeholder="0"
                class="duration-input"
                @input="handleScheduleHoursInput"
                @blur="updateScheduleCostTime"
              />
              <span class="duration-label">小时</span>
              <el-input
                v-model="scheduleDurationMinutesDisplay"
                placeholder="0"
                class="duration-input"
                @input="handleScheduleMinutesInput"
                @blur="updateScheduleCostTime"
              />
              <span class="duration-label">分钟</span>
            </div>
      </el-form-item>
      <el-form-item label="Bom编码" prop="bomId">
        <el-select
          v-model="formData.bomCode"
          placeholder="请选择Bom编码"
          clearable
          class="!w-240px"
          @change="(val: number) => {
            const selectedBom = bomList.find((bom: any) => bom.code === val)
            if (selectedBom) {
              formData.bomId = selectedBom.id
              formData.bomVersion = selectedBom.versionNumber || '1.0'
            } else {
              formData.bomId = undefined
              formData.bomVersion = undefined
            }
          }"
        >
          <el-option
            v-for="dict in bomList"
            :key="dict.id"
            :label="dict.name ||dict.materialName + ' - ' + dict.code + ' - ' + (dict.versionNumber || '1.0')"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计划数量" prop="scheduleQuantity">
        <el-input v-model="formData.scheduleQuantity" placeholder="请输入计划数量" class="!w-240px">
          <template #append>
            <el-select v-model="formData.productUnit" placeholder="请选择产品单位" class="!w-100px" disabled>
            <el-option
              v-for="dict in unitList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="每槽数量" prop="slotQuantity">
        <el-input v-model="formData.slotQuantity" placeholder="请输入每槽数量" class="!w-240px" type="number">
          <template #append>
            <el-select v-model="formData.productUnit" placeholder="请选择产品单位" class="!w-100px" disabled>
            <el-option
              v-for="dict in unitList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value" 
            />
          </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="计划总槽数" prop="slotCount">
        <el-input v-model="formData.slotCount" placeholder="请输入计划总槽数" class="!w-240px" disabled/>
      </el-form-item>
      <el-form-item label="计划件数" prop="schedulePiece">
        <el-input v-model="formData.schedulePiece" placeholder="请输入计划件数" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="计划产线" prop="scheduleLine">
        <el-select
          v-model="formData.scheduleLine"
          placeholder="请选择生产线"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.MANUFACTURE_LINE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计划用人" prop="scheduleHeadcount">
        <el-input v-model="formData.scheduleHeadcount" placeholder="请输入计划用人" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="生产要求" prop="requirement">
        <el-input v-model="formData.requirement" placeholder="请输入生产要求" class="!w-240px"/>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" class="!w-240px"/>
      </el-form-item>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="任务单明细" name="workOrderDetail">
        <WorkOrderDetailForm
          ref="workOrderDetailFormRef"
          :biz-order-id="formData.id"
          :bom-id="formData.bomId"
          :is-transfer-mode="formType === 'transferMfg'"
          :slot-quantity="formData.slotQuantity"
          :order-quantity="formData.scheduleQuantity"
          :order-spec-quantity="formData.schedulePiece"
          :unit-list="unitList"
        />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { watch, nextTick } from 'vue' // 确保已导入
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { WorkOrderApi, WorkOrderVO } from '@/api/scm/mfg/workorder'
import { RequestOrderApi } from '@/api/scm/mfg/requestorder'
import WorkOrderDetailForm from './components/WorkOrderDetailForm.vue'
import ScrollSelect from '@/components/ScrollSelect/index.vue'
import {OrderDetailApi, OrderDetailVO} from '@/api/scm/sale/orderdetail'
import { UnitApi } from '@/api/scm/base/unit'
import { BomApi } from '@/api/scm/mfg/bom'
import { MaterialApi } from '@/api/scm/base/material'
/** 生产任务 表单 */
defineOptions({ name: 'WorkOrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const productDisabled = ref(false) // 产品选择框是否禁用
const formData = ref({
  id: undefined,
  workNo: undefined, // 生产单号
  planId: undefined,
  customerId: undefined,
  customerName: undefined,
  orderType: 'mfg_request_order', // 默认来源类型为销售订单
  orderDetailId: undefined, // 销售订单明细ID
  orderId: undefined,
  orderNo: undefined,
  requestId: undefined,
  requestNo:undefined,
  productId: undefined,
  productCode: undefined,
  productName: undefined,
  productSubType: undefined,
  productUnit: undefined,
  spec: undefined,
  status: undefined,
  bomId: undefined,
  bomCode: undefined,
  bomVersion: undefined,
  progress: undefined,
  orderDate: undefined,
  orderQuantity: undefined,
  orderUnit: undefined,
  deliverDate: undefined,
  scheduleStartDate: undefined as number | undefined,
  scheduleStartTime: undefined as number | undefined,
  scheduleEndDate: undefined as number | undefined,
  scheduleEndTime: undefined as number | undefined,
  scheduleQuantity: undefined,
  schedulePiece: undefined,
  scheduleCostTime: 0,
  scheduleLine: undefined,
  scheduleHeadcount: undefined,
  requirement: undefined,
  remark: undefined,
  actualLine: undefined,
  actualQuantity: undefined,
  actualStartTime: undefined,
  actualEndTime: undefined,
  actualCostTime: undefined,
  actualHeadcount: undefined,
  actualPiece: undefined,
  actualBatchNo: undefined,
  actualRemark: undefined,
  shareImageUrl: undefined,
  scheduleTimeRange: [] as number[], 
  bomLabel: null as string | null, // 用于显示的Bom编码
  slotQuantity: 1, // 每槽数量
  slotCount: undefined as number | undefined, // 计划总槽数
})
const formRules = reactive({
  productId: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  productName: [{ required: true, message: '产品名称不能为空', trigger: 'blur' }],
  bomCode: [{ required: true, message: 'Bom编码不能为空', trigger: 'blur' }],
  scheduleQuantity: [{ required: true, message: '计划数量不能为空', trigger: 'blur' }],
  // schedulePiece: [{ required: true, message: '计划件数不能为空', trigger: 'blur' }],
  scheduleTimeRange: [{ required: true, message: '计划开始日期不能为空', trigger: 'blur' }],
  slotQuantity: [{ required: true, message: '每槽数量不能为空', trigger: 'blur' }],
  slotCount: [{ required: true, message: '计划总槽数不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

// 计划用时输入相关变量
const scheduleDurationHoursDisplay = ref<string>('0')
const scheduleDurationMinutesDisplay = ref<string>('0')

/** 子表的表单 */
const subTabsName = ref('workOrderDetail')
const workOrderDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, idOrData?: number | any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 按需加载单位数据
  if (unitList.value.length === 0) {
    await getRemoteUnit()
  }

  // 处理不同类型的操作
  if (type === 'transferMfg') {
    formLoading.value = true
    try {
      if (typeof idOrData === 'number') {
        // 转生产：从请求订单ID获取详细数据并预填充表单
        const requestOrderId = idOrData
        // 从API获取请求订单的详细数据
        const requestOrderData = await RequestOrderApi.getRequestOrder(requestOrderId)
        if (requestOrderData) {
          // 预填充表单数据（只填充业务相关数据，不填充自动生成的字段）
          formData.value.orderId = requestOrderData.id
          formData.value.orderNo = requestOrderData.orderNo
          formData.value.requestNo = requestOrderData.requestNo
          formData.value.requestId = requestOrderData.id
          formData.value.customerId = requestOrderData.customerId
          formData.value.customerName = requestOrderData.customerName
          formData.value.productId = requestOrderData.productId
          formData.value.productCode = requestOrderData.productCode
          formData.value.productName = requestOrderData.productName
          formData.value.productSubType = requestOrderData.productSubType
          formData.value.productUnit = requestOrderData.productUnit
          formData.value.spec = requestOrderData.spec
          formData.value.orderDate = requestOrderData.orderDate
          formData.value.orderQuantity = requestOrderData.orderQuantity
          formData.value.orderUnit = requestOrderData.orderUnit
          formData.value.deliverDate = requestOrderData.deliverDate
          formData.value.scheduleQuantity = requestOrderData.orderQuantity // 计划数量默认为订单数量
          formData.value.schedulePiece = requestOrderData.orderSpecQuantity
          formData.value.bomId = requestOrderData.bomId
          formData.value.bomCode = requestOrderData.bomCode
          formData.value.bomVersion = requestOrderData.bomVersion
          formData.value.requirement = requestOrderData.requirement
          formData.value.remark = requestOrderData.remark

          // 设置默认的计划时间范围（当前时间到交货日期）
          const now = Date.now()
          const deliverTime = requestOrderData.deliverDate ? new Date(requestOrderData.deliverDate).getTime() : now + 7 * 24 * 60 * 60 * 1000 // 默认7天后
          formData.value.scheduleTimeRange = [now, deliverTime]
          formData.value.scheduleStartDate = now
          formData.value.scheduleEndDate = deliverTime

          // 禁用产品选择框，因为已经从请求订单确定了产品
          productDisabled.value = true

          // 如果有产品ID，加载BOM信息
          if (requestOrderData.productId) {
            await getRemoteBom(requestOrderData.productId)
          }

          // 获取需求订单详情数据（包含每槽用量等信息）
          if (requestOrderData.id) {
            try {
              const requestOrderDetails = await RequestOrderApi.getRequestOrderDetailListByBizOrderId(requestOrderData.id)

              // 在下一个tick中设置详情数据到子表单
              await nextTick()
              if (workOrderDetailFormRef.value && requestOrderDetails && requestOrderDetails.length > 0) {
                // 将需求订单详情数据转换为工作订单详情格式
                const workOrderDetails = requestOrderDetails.map((detail: any) => ({
                  ...detail,
                  // 确保字段映射正确
                  materialId: detail.materialId,
                  materialName: detail.materialName,
                  materialCode: detail.materialCode,
                  materialType: detail.materialType,
                  spec: detail.spec,
                  unit: detail.unit,
                  standardUnitId: detail.standardUnitId,
                  warehouseId: detail.warehouseId,
                  locationId: detail.locationId,

                  // 数量相关字段
                  plannedQuantity: detail.plannedQuantity || 0,
                  fulfilledQuantity: detail.fulfilledQuantity || 0,
                  pendingQuantity: detail.pendingQuantity || 0,
                  readyQuantity: detail.readyQuantity || 0,
                  slotQuantity: detail.slotQuantity || 0, // 每槽用量
                  slotSpecQuantity: detail.slotSpecQuantity || 0,

                  // 库存相关字段
                  stockQuantity: detail.stockQuantity || 0,
                  shortageQuantity: detail.shortageQuantity || 0,
                  purchaseQuantity: detail.purchaseQuantity || 0,
                  transitQuantity: detail.transitQuantity || 0,
                  lockStockQuantity: detail.lockStockQuantity || 0,
                  lockTransitQuantity: detail.lockTransitQuantity || 0,

                  // 状态和其他字段
                  readyStatus: detail.readyStatus || 0,
                  lossRate: detail.lossRate || 0,
                  lossQuantity: detail.lossQuantity || 0,
                  unitPrice: detail.unitPrice || 0,
                  amount: detail.amount || 0,
                  remark: detail.remark || '',
                  note: detail.note || '',
                  batchNo: detail.batchNo || '',
                  num: detail.num || 0,
                  version: detail.version || 1
                }))

                // 设置详情数据到子表单
                workOrderDetailFormRef.value.setDetails(workOrderDetails)
                
                // 触发子表重新计算，确保使用正确的计算逻辑
                setTimeout(() => {
                  if (workOrderDetailFormRef.value && workOrderDetailFormRef.value.recalculateAll) {
                    workOrderDetailFormRef.value.recalculateAll()
                  }
                }, 100)
              }
            } catch (error) {
              console.error('获取需求订单详情失败:', error)
              // 如果获取详情失败，继续使用BOM数据作为备选方案
            }
          }
        }
      } else if (idOrData && typeof idOrData === 'object') {
        // 直接使用传入的对象数据
        console.log('idOrData:', idOrData)
        // 预填充表单数据
        formData.value.orderId = idOrData.orderId || idOrData.id
        formData.value.orderNo = idOrData.orderNo || idOrData.requestNo
        formData.value.requestNo = idOrData.requestNo
        formData.value.requestId = idOrData.id
        formData.value.customerId = idOrData.customerId
        formData.value.customerName = idOrData.customerName
        formData.value.productId = idOrData.productId
        formData.value.productCode = idOrData.productCode
        formData.value.productName = idOrData.productName
        formData.value.productSubType = idOrData.productSubType
        formData.value.productUnit = idOrData.productUnit
        formData.value.spec = idOrData.spec
        formData.value.orderDate = idOrData.orderDate
        formData.value.orderQuantity = idOrData.orderQuantity || idOrData.quantity
        formData.value.orderUnit = idOrData.orderUnit || idOrData.productUnit
        formData.value.deliverDate = idOrData.deliverDate
        
        // 计划数据
        formData.value.scheduleQuantity = idOrData.scheduleQuantity || idOrData.quantity || idOrData.orderQuantity || idOrData.pendingQuantity
        formData.value.schedulePiece = idOrData.schedulePiece || idOrData.orderSpecQuantity
        
        // BOM数据
        formData.value.bomId = idOrData.bomId
        formData.value.bomCode = idOrData.bomCode
        formData.value.bomVersion = idOrData.bomVersion
        
        // 其他数据
        formData.value.requirement = idOrData.requirement
        formData.value.remark = idOrData.remark
        
        // 设置默认的计划时间范围
        if (idOrData.scheduleTimeRange) {
          formData.value.scheduleTimeRange = idOrData.scheduleTimeRange
          formData.value.scheduleStartDate = idOrData.scheduleStartDate || idOrData.scheduleTimeRange[0]
          formData.value.scheduleEndDate = idOrData.scheduleEndDate || idOrData.scheduleTimeRange[1]
        } else {
          const now = Date.now()
          const deliverTime = idOrData.deliverDate ? new Date(idOrData.deliverDate).getTime() : now + 7 * 24 * 60 * 60 * 1000
          formData.value.scheduleTimeRange = [now, deliverTime]
          formData.value.scheduleStartDate = now
          formData.value.scheduleEndDate = deliverTime
        }
        
        // 禁用产品选择框
        productDisabled.value = true
        
        // 如果有产品ID，加载BOM信息
        if (idOrData.productId) {
          await getRemoteBom(idOrData.productId)
        }
        
        // 如果有物料明细，处理物料明细
        if (idOrData.details && idOrData.details.length > 0) {
          // 在子表单中处理物料明细
          await nextTick()
          if (workOrderDetailFormRef.value) {
            workOrderDetailFormRef.value.setDetails(idOrData.details)
          }
        } else if (idOrData.id || idOrData.orderId) {
          // 如果没有传入详情数据，但有订单ID，尝试获取需求订单详情
          try {
            const bizOrderId = idOrData.id || idOrData.orderId
            const requestOrderDetails = await RequestOrderApi.getRequestOrderDetailListByBizOrderId(bizOrderId)

            if (requestOrderDetails && requestOrderDetails.length > 0) {
              // 转换数据格式
              const workOrderDetails = requestOrderDetails.map((detail: any) => ({
                ...detail,
                // 确保字段映射正确
                materialId: detail.materialId,
                materialName: detail.materialName,
                materialCode: detail.materialCode,
                materialType: detail.materialType,
                spec: detail.spec,
                unit: detail.unit,
                standardUnitId: detail.standardUnitId,
                warehouseId: detail.warehouseId,
                locationId: detail.locationId,

                // 数量相关字段
                plannedQuantity: detail.plannedQuantity || 0,
                fulfilledQuantity: detail.fulfilledQuantity || 0,
                pendingQuantity: detail.pendingQuantity || 0,
                readyQuantity: detail.readyQuantity || 0,
                slotQuantity: detail.slotQuantity || 0, // 每槽用量
                slotSpecQuantity: detail.slotSpecQuantity || 0,

                // 库存相关字段
                stockQuantity: detail.stockQuantity || 0,
                shortageQuantity: detail.shortageQuantity || 0,
                purchaseQuantity: detail.purchaseQuantity || 0,
                transitQuantity: detail.transitQuantity || 0,
                lockStockQuantity: detail.lockStockQuantity || 0,
                lockTransitQuantity: detail.lockTransitQuantity || 0,

                // 状态和其他字段
                readyStatus: detail.readyStatus || 0,
                lossRate: detail.lossRate || 0,
                lossQuantity: detail.lossQuantity || 0,
                unitPrice: detail.unitPrice || 0,
                amount: detail.amount || 0,
                remark: detail.remark || '',
                note: detail.note || '',
                batchNo: detail.batchNo || '',
                num: detail.num || 0,
                version: detail.version || 1
              }))

              // 设置详情数据到子表单
              await nextTick()
              if (workOrderDetailFormRef.value) {
                workOrderDetailFormRef.value.setDetails(workOrderDetails)
                
                // 触发子表重新计算，确保使用正确的计算逻辑
                setTimeout(() => {
                  if (workOrderDetailFormRef.value && workOrderDetailFormRef.value.recalculateAll) {
                    workOrderDetailFormRef.value.recalculateAll()
                  }
                }, 100)
              }
            }
          } catch (error) {
            console.error('从对象数据获取需求订单详情失败:', error)
          }
        }
      }

    } catch (error) {
      message.error('获取或处理数据失败，请重试')
    } finally {
      formLoading.value = false
    }
  } else if (idOrData && typeof idOrData === 'number') {
    // 修改时，设置数据
    const id = idOrData
    formLoading.value = true
    try {
      const workOrderData = await WorkOrderApi.getWorkOrder(id)
      if (workOrderData) {
        formData.value = workOrderData
        if (formData.value.scheduleStartDate && formData.value.scheduleEndDate) {
          formData.value.scheduleTimeRange = [
            formData.value.scheduleStartDate,
            formData.value.scheduleEndDate
          ]
        }

        // 处理计划用时数据：更新小时分钟输入框
        if (workOrderData.scheduleCostTime) {
          const minutes = parseInt(workOrderData.scheduleCostTime.toString(), 10) || 0
          formData.value.scheduleCostTime = minutes
          updateScheduleDurationInputs(minutes)
        }

        // 如果有产品ID，加载对应的BOM列表
        if (workOrderData.productId) {
          await getRemoteBom(workOrderData.productId)
        }

        // 确保工单详情数据正确加载 - 等待下一个tick后触发子表单数据加载
        await nextTick()
        if (workOrderDetailFormRef.value && formData.value.id) {
          // 手动触发子表单重新加载数据，确保规格等信息正确显示
          try {
            const workOrderDetails = await WorkOrderApi.getWorkOrderDetailListByBizOrderId(formData.value.id)
            if (workOrderDetails && Array.isArray(workOrderDetails)) {
              workOrderDetailFormRef.value.setDetails(workOrderDetails)
            }
          } catch (detailError) {
            console.error('加载工单详情失败:', detailError)
          }
        }
      }
    } catch (error) {
      // 确保 formData.value 不为 null
      if (!formData.value) {
        resetForm()
      }
    } finally {
      formLoading.value = false
    }
  } else if (type === 'create') {
    // 新增时，如果没有预填充数据，确保BOM列表为空
    bomList.value = []
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
   // 转换时间范围到原有字段
  if (formData.value.scheduleTimeRange) {
    formData.value.scheduleStartTime = formData.value.scheduleTimeRange[0]
    formData.value.scheduleEndTime = formData.value.scheduleTimeRange[1]
  }
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await workOrderDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'workOrderDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const rawData = formData.value as unknown as WorkOrderVO
    // 拼接子表的数据
    rawData.workOrderDetails = workOrderDetailFormRef.value.getData()

    // 清理数据，移除无效字段
    const data = cleanWorkOrderData(rawData, formType.value)

    if (formType.value === 'create' || formType.value === 'transferMfg') {
      await WorkOrderApi.createWorkOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await WorkOrderApi.updateWorkOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/**
 * 清理工单数据，移除无效字段
 * @param data 原始数据
 * @param formType 表单类型
 * @returns 清理后的数据
 */
const cleanWorkOrderData = (data: any, formType: string) => {
  // 创建一个新对象来存储有效字段
  const cleanedData: any = {}

  // 根据接口文档定义的字段列表
  const validFields = [
    'id', 'workNo', 'planId', 'planNo', 'customerId', 'customerName',
    'orderType', 'orderId', 'orderNo', 'productId', 'productCode',
    'productName', 'productSubType', 'productUnit', 'spec', 'status',
    'bomId', 'bomCode', 'bomVersion', 'progress', 'orderDate',
    'orderQuantity', 'orderUnit', 'deliverDate', 'scheduleStartDate',
    'scheduleStartTime', 'scheduleEndDate', 'scheduleEndTime',
    'scheduleQuantity', 'schedulePiece', 'scheduleCostTime',
    'scheduleLine', 'scheduleHeadcount', 'requirement', 'remark',
    'actualLine', 'actualQuantity', 'actualStartTime', 'actualEndTime',
    'actualCostTime', 'actualHeadcount', 'actualPiece', 'actualBatchNo',
    'actualRemark', 'shareImageUrl', 'slotQuantity', 'slotCount',
    'approveStatus', 'pickingStatus', 'inStockStatus', 'reportStatus',
    'qualityStatus', 'approverName','requestId','requestNo',
  ]

  // 如果是转生产操作，暂时保留 requestNo 字段
  if (formType === 'transferMfg') {
    validFields.push('requestNo')
  }

  // 复制有效字段
  validFields.forEach(field => {
    if (data[field] !== undefined) {
      cleanedData[field] = data[field]
    }
  })

  // 处理工单明细
  if (data.workOrderDetails && Array.isArray(data.workOrderDetails)) {
    // 定义工单明细的有效字段
    const validDetailFields = [
      'id', 'num', 'bizOrderId', 'bizOrderNo', 'warehouseId', 'locationId',
      'materialId', 'materialName', 'materialCode', 'materialType', 'spec', 'unit',
      'unitPrice', 'amount', 'remark', 'note', 'batchNo',

      // 数量相关字段
      'quantity', 'plannedQuantity', 'fulfilledQuantity', 'pendingQuantity',
      'standardPlannedQuantity', 'standardFulfilledQuantity', 'standardUnit',
      'plannedSpecQuantity', 'fulfilledSpecQuantity',

      // 每槽数量相关字段
      'slotQuantity', 'slotSpecQuantity',

      // 锁定数量相关字段
      'lockStockQuantity', 'lockTransitQuantity',

      // 库存相关字段
      'readyQuantity', 'stockQuantity', 'shortageQuantity',
      'purchaseQuantity', 'transitQuantity', 'lockStockQuantity',

      // 状态相关字段
      'readyStatus', 'lossRate', 'lossQuantity',

      // 税务和发票字段
      'taxPrice', 'taxAmount', 'invoiceQuantity', 'invoiceAmount', 'standardInvoiceQuantity',

      // 成本和会计字段
      'costObjectId', 'costObjectName', 'accountingVoucherNumber',

      // 金蝶相关字段
      'kdId', 'kdOrderId',

      // 版本字段
      'version'
    ]

    // 清理每个明细项
    cleanedData.workOrderDetails = data.workOrderDetails.map((detail: any) => {
      const cleanedDetail: any = {}
      validDetailFields.forEach(field => {
        if (detail[field] !== undefined) {
          // 对规格数量字段进行特殊处理，保持原始格式
          if (field === 'plannedSpecQuantity' || field === 'slotSpecQuantity' || field === 'fulfilledSpecQuantity') {
            // 保持原始数据格式，不进行强制转换
            // 如果是字符串格式（如 "6袋+19千克"），保持字符串格式
            // 如果是数值格式，保持数值格式
            cleanedDetail[field] = detail[field];
          } else {
            cleanedDetail[field] = detail[field]
          }
        }
      })
      return cleanedDetail
    })
  }

  return cleanedData
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    workNo: undefined, // 生产单号
    planId: undefined,
    customerId: undefined,
    customerName: undefined,
    orderType: 'mfg_request_order',
    orderDetailId: undefined, // 销售订单明细ID
    orderId: undefined,
    orderNo: undefined,
    requestNo: undefined, // 请求订单编号
    requestId: undefined, // 请求订单ID
    productId: undefined,
    productCode: undefined,
    productName: undefined,
    productSubType: undefined,
    productUnit: undefined,
    spec: undefined,
    status: undefined,
    bomId: undefined,
    bomCode: undefined,
    bomVersion: undefined,
    progress: undefined,
    orderDate: undefined,
    orderQuantity: undefined,
    orderUnit: undefined,
    deliverDate: undefined,
    scheduleStartDate: undefined as number | undefined,
    scheduleStartTime: undefined as number | undefined,
    scheduleEndDate: undefined as number | undefined,
    scheduleEndTime: undefined as number | undefined,
    scheduleQuantity: undefined,
    schedulePiece: undefined,
    scheduleCostTime: 0,
    scheduleLine: undefined,
    scheduleHeadcount: undefined,
    requirement: undefined,
    remark: undefined,
    actualLine: undefined,
    actualQuantity: undefined,
    actualStartTime: undefined,
    actualEndTime: undefined,
    actualCostTime: undefined,
    actualHeadcount: undefined,
    actualPiece: undefined,
    actualBatchNo: undefined,
    actualRemark: undefined,
    shareImageUrl: undefined,
    scheduleTimeRange: [] as number[], // 重置计划时间范围
    bomLabel: null as string | null, // 重置Bom编码
    slotQuantity: 1, // 重置每槽数量
    slotCount: undefined as number | undefined, // 重置计划总槽数
  }
  formRef.value?.resetFields()
  productDisabled.value = false // 重置为可编辑状态

}

const getRemoteSaleOrder = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await OrderDetailApi.getSimpleOrderDetailPage({
    pageNo,
    pageSize,
    productName: query,
    ...restParams,
  });
  const { list, total } = await response;
  
  // 确保每条数据都有 orderNo、productName 和 customerName 字段
  const formattedList = list.map(item => ({
    ...item,
    productName: item.productName || '无产品名称',
    customerName: item.customerName || '无客户名称',
    orderNo: item.orderNo || '无订单编号'
  }));
  
  return { list: formattedList, total };
}

// 从单位接口获取单位列表
const unitList = ref([])
const getRemoteUnit = async () => {
  // 检查是否已经加载过单位数据，避免重复请求
  if (unitList.value.length > 0) {
    console.log('单位数据已缓存，跳过重复请求')
    return
  }

  const data = await UnitApi.getUnitPage({ pageSize: 100, pageNo: 1 });
  unitList.value = data.list.map(item => ({
    value: item.id,
    label: item.name
  }))
  console.log('unitList', unitList.value)
}

// 获取bom列表
const bomList = ref<any[]>([])
const getRemoteBom = async (materialId: number | undefined) => {
  if (!materialId) {
    bomList.value = []
    return
  }
  const data = await BomApi.getSimpleBomPage({ pageSize: 20, pageNo: 1, materialId: materialId });
  bomList.value = data.list
  console.log('bomList loaded:', bomList.value)

  // 如果已经有bomId但没有bomCode，或者bomCode不在列表中，则自动选择
  if (formData.value.bomId && bomList.value.length > 0) {
    const selectedBom = bomList.value.find((bom: any) => bom.id === formData.value.bomId)
    if (selectedBom && !formData.value.bomCode) {
      formData.value.bomCode = selectedBom.code
      formData.value.bomVersion = selectedBom.versionNumber || '1.0'
      console.log('自动选中BOM:', selectedBom)
    }
  }
  // 如果没有选中任何BOM，但有BOM列表，可以选择第一个作为默认值（可选）
  else if (!formData.value.bomId && bomList.value.length > 0 && formType.value === 'transferMfg') {
    const firstBom = bomList.value[0]
    formData.value.bomId = firstBom.id
    formData.value.bomCode = firstBom.code
    formData.value.bomVersion = firstBom.versionNumber || '1.0'
    console.log('默认选中第一个BOM:', firstBom)
  }
}

const getRemoteMaterial = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await MaterialApi.getSimpleMaterialPage({
    pageNo,
    pageSize,
    name: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}

// 计划用时相关方法
// 处理小时输入
const handleScheduleHoursInput = (value: string) => {
  // 只允许数字输入
  const numValue = value.replace(/[^\d]/g, '')
  scheduleDurationHoursDisplay.value = numValue
}

// 处理分钟输入
const handleScheduleMinutesInput = (value: string) => {
  // 只允许数字输入，并限制最大值为59
  let numValue = value.replace(/[^\d]/g, '')
  if (parseInt(numValue) > 59) {
    numValue = '59'
  }
  scheduleDurationMinutesDisplay.value = numValue
}

// 手动更新计划用时（从小时分钟输入框）
const updateScheduleCostTime = () => {
  const hours = parseInt(scheduleDurationHoursDisplay.value) || 0
  const minutes = parseInt(scheduleDurationMinutesDisplay.value) || 0
  const totalMinutes = hours * 60 + minutes
  formData.value.scheduleCostTime = totalMinutes
}

// 从计划用时分钟数更新小时分钟输入框
const updateScheduleDurationInputs = (totalMinutes: number) => {
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  scheduleDurationHoursDisplay.value = hours.toString()
  scheduleDurationMinutesDisplay.value = minutes.toString()
}

// 计算计划用时（从计划开始结束时间自动计算）
const calculateScheduleCostTime = () => {
  if (formData.value.scheduleTimeRange && formData.value.scheduleTimeRange.length === 2) {
    const startTime = new Date(formData.value.scheduleTimeRange[0])
    const endTime = new Date(formData.value.scheduleTimeRange[1])

    // 计算时间差（毫秒）
    const timeDiff = endTime.getTime() - startTime.getTime()

    if (timeDiff > 0) {
      // 转换为分钟，精确到分钟
      const minutes = Math.round(timeDiff / (1000 * 60))

      // 更新计划用时字段（存储分钟数）
      formData.value.scheduleCostTime = minutes

      // 更新小时分钟输入框
      updateScheduleDurationInputs(minutes)
    } else {
      formData.value.scheduleCostTime = 0
      scheduleDurationHoursDisplay.value = '0'
      scheduleDurationMinutesDisplay.value = '0'
    }
  } else {
    formData.value.scheduleCostTime = 0
    scheduleDurationHoursDisplay.value = '0'
    scheduleDurationMinutesDisplay.value = '0'
  }
}

// 计算计划总槽数
const calculateQuantity = () => {
  console.log('计算计划总槽数')
  if (!formData.value) {
    return
  }
  const { scheduleQuantity, slotQuantity } = formData.value
  if (!scheduleQuantity || !slotQuantity || slotQuantity <= 0) {
    if (formData.value) {
      formData.value.slotCount = undefined
    }
    return
  }
  // 向上取整
  if (formData.value) {
    formData.value.slotCount = Math.ceil(scheduleQuantity / slotQuantity)
  }
  
  // 通知子表重新计算
  nextTick(() => {
    if (workOrderDetailFormRef.value && workOrderDetailFormRef.value.recalculateAll) {
      workOrderDetailFormRef.value.recalculateAll()
    }
  })
}

//如果计划数量改变，或者每槽数量改变，计算总槽数
// 改为深度监听单独字段，更可靠
watch(
  () => ({
    q: formData.value?.scheduleQuantity,
    s: formData.value?.slotQuantity
  }),
  () => {
    console.log('检测到数量变化') // 调试用
    calculateQuantity()
  },
  { immediate: true, deep: true }
)

// 移除组件挂载时的单位数据初始化，改为在打开弹窗时按需加载
</script>
<style scoped>
/* 子表容器样式 */
.sub-form-tabs {
  display: flex;
  height: 400px; /* 固定高度 */
  flex-direction: column;
}

/* 子表内容区域样式 */
.sub-form-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: auto;
}

/* 子表表单样式 */
.sub-form-tabs :deep(.el-form) {
  height: 100%;
  overflow: auto;
}

/* 计划用时输入样式 */
.duration-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.duration-input {
  width: 80px !important;
}

.duration-label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

@media (max-width: 768px) {
  .duration-input-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .duration-input {
    width: 100px !important;
  }

  .duration-label {
    margin-right: 0;
    margin-left: 4px;
  }
}
</style>
