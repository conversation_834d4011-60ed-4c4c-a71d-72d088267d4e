<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table
      :data="formData"
      class="-mt-10px"
      border
      :max-height="400"
      style="width: 100%"
    >
      <el-table-column label="序号" min-width="60" align="center" prop="num" />
      <el-table-column label="物料信息" min-width="250">
        <template #default="{ row }">
          <div class="material-info">
            <div class="material-name">
              <span class="label">名称：</span>
              <span class="value">{{ row.materialName || '-' }}</span>
            </div>
            <div class="material-code">
              <span class="label">编码：</span>
              <span class="value">{{ row.materialCode || '-' }}</span>
            </div>
            <div class="material-spec">
              <span class="label">规格：</span>
              <span class="value">{{ row.spec || '-' }}</span>
            </div>
            <div class="planned-quantity">
              <span class="label">计划：</span>
              <span class="value">{{ row.plannedQuantity || '-' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="损耗数量" min-width="160">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.lossQuantity`" :rules="formRules.lossQuantity" class="mb-0px!">
            <el-input-number
              v-model="row.lossQuantity"
              placeholder="损耗数量"
              :precision="4"
              :min="0"
              :disabled="disabled"
              class="!w-full"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="损耗单位" min-width="100">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.lossUnitName`" class="mb-0px!">
            <el-input v-model="row.lossUnitName" placeholder="损耗单位" disabled />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" class="mb-0px!">
            <el-input
              v-model="row.remark"
              placeholder="请输入备注"
              :disabled="disabled"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="60" v-if="!disabled" fixed="right">
        <template #default="{ $index }">
          <el-button
            link
            type="danger"
            @click="handleDelete($index)"
            :disabled="disabled"
            size="small"
          >
            <Icon icon="ep:delete" />
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <!-- 不再显示添加按钮，因为损耗物料来自投料单，不允许手动添加 -->
</template>

<script setup lang="ts">
import { WorkOrderApi } from '@/api/scm/mfg/workorder'
import { getRemoteUnitMap } from '@/utils/commonBiz'

const props = defineProps<{
  reportId?: number // 报工单ID
  workId?: number // 工单ID，用于获取投料单数据
  disabled?: boolean // 是否禁用
}>()


const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive({
  materialId: [{ required: true, message: '物料不能为空', trigger: 'change' }],
  lossQuantity: [{ required: true, message: '损耗数量不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

// 单位映射缓存
const unitMap = ref<Record<number, any>>({})

// 批量获取单位映射
const getUnitMap = async () => {
  if (Object.keys(unitMap.value).length > 0) {
    return unitMap.value // 如果已经缓存，直接返回
  }

  try {
    unitMap.value = await getRemoteUnitMap()
    return unitMap.value
  } catch (error) {
    console.error('获取单位列表失败:', error)
    return {}
  }
}

// 根据单位ID获取单位名称
const getUnitNameById = (unitId: number) => {
  return unitMap.value[unitId]?.name || ''
}



/** 监听工单ID的变化，从投料单获取数据 */
watch(
  () => props.workId,
  async (workId) => {
    // 重置表单
    formData.value = []

    if (!workId) return

    try {
      formLoading.value = true

      // 批量获取单位映射（只在第一次或缓存为空时请求）
      await getUnitMap()

      // 从投料单获取数据
      const feedData = await WorkOrderApi.getWorkOrderDetailListByBizOrderId(workId)
      if (Array.isArray(feedData) && feedData.length > 0) {
        // 将投料单数据转换为报工损耗数据格式，并直接设置单位名称
        const lossDataFromFeed = feedData.map((item: any, index: number) => ({
          id: undefined, // 新数据，没有ID
          num: index + 1,
          materialId: item.materialId,
          materialName: item.materialName,
          materialCode: item.materialCode,
          spec: item.spec,
          plannedQuantity: item.plannedQuantity || item.quantity || 0, // 计划数量
          lossQuantity: 0, // 默认损耗数量为0，用户需要手动输入
          lossUnit: item.unit,
          lossUnitName: getUnitNameById(item.unit), // 直接从缓存中获取单位名称
          remark: '',
        }))

        formData.value = lossDataFromFeed
      }
    } catch (error) {
      console.error('从投料单获取数据失败:', error)
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)





/** 删除按钮操作 */
const handleDelete = (index: number) => {
  // 直接从列表中移除
  formData.value.splice(index, 1)
  // 重新计算序号
  formData.value.forEach((item, idx) => {
    item.num = idx + 1
  })
}

/** 表单校验 */
const validate = async () => {
  return await formRef.value.validate()
}

/** 获取表单数据 */
const getData = () => {
  return formData.value.filter(item => item.materialId) // 过滤掉未选择物料的行
}

/** 设置表单数据 */
const setData = async (data: any[]) => {
  // 确保单位映射已加载
  await getUnitMap()

  formData.value = data.map((item, index) => ({
    ...item,
    num: index + 1,
    // 如果没有单位名称但有单位ID，则从缓存中获取
    lossUnitName: item.lossUnitName || getUnitNameById(item.lossUnit)
  }))
}

defineExpose({ validate, getData, setData })
</script>

<style lang="scss" scoped>
.material-info {
  .material-name,
  .material-code,
  .material-spec,
  .planned-quantity {
    display: flex;
    align-items: center;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 4px;

    .label {
      color: #909399;
      font-weight: 500;
      min-width: 36px;
      flex-shrink: 0;
    }

    .value {
      color: #606266;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .planned-quantity {
    margin-bottom: 0;
  }
}
</style>
