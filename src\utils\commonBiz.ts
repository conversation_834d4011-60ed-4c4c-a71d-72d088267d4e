import { MaterialApi } from '@/api/scm/base/material';
import {UnitApi, UnitPageVO} from '@/api/scm/base/unit';
import {WarehouseApi, WarehousePageVO} from '@/api/scm/inventory/warehouse';
import {ApplyApi, ApplyVO} from '@/api/scm/quote/apply';
export const getRemoteMaterial = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await MaterialApi.getSimpleMaterialPage({
    pageNo,
    pageSize,
    name: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}

export const getRemoteUnitListAndMap = async () => {
  const data = await UnitApi.getUnitPage({ pageSize: 100, pageNo: 1 });
  return {
    list: data.list,
    map: data.list.reduce((acc, cur) => {
      acc[cur.id] = cur;
      return acc;
    }, {})
  }
}

export const getRemoteUnit = async () => {
  const data = await UnitApi.getUnitPage({ pageSize: 100, pageNo: 1 });
  return data.list
}

export const getRemoteUnitMap = async () => {
  const data = await UnitApi.getUnitPage({ pageSize: 100, pageNo: 1 });
  return data.list.reduce((acc, cur) => {
    acc[cur.id] = cur;
    return acc;
  }, {});
}

// 仓库数据缓存
let warehouseCache: any[] = []
let warehouseCacheTime = 0
let warehouseMapCache: Record<number, any> = {}
const WAREHOUSE_CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存

export const getWarehouse = async () => {
  const now = Date.now()
  // 检查缓存是否有效
  if (warehouseCache.length > 0 && (now - warehouseCacheTime) < WAREHOUSE_CACHE_DURATION) {
    return warehouseCache
  }

  try {
    const data = await WarehouseApi.getWarehouseList();
    // 更新缓存
    warehouseCache = data || []
    warehouseCacheTime = now
    return warehouseCache
  } catch (error) {
    console.error('获取仓库列表失败:', error)
    return warehouseCache // 返回缓存数据，即使可能过期
  }
}

export const getWarehouseMap = async () => {
  const now = Date.now()
  // 检查缓存是否有效
  if (Object.keys(warehouseMapCache).length > 0 && (now - warehouseCacheTime) < WAREHOUSE_CACHE_DURATION) {
    return warehouseMapCache
  }

  try {
    const data = await getWarehouse() // 复用 getWarehouse 的缓存逻辑
    warehouseMapCache = data.reduce((acc, cur) => {
      acc[cur.id] = cur;
      return acc;
    }, {});
    return warehouseMapCache
  } catch (error) {
    console.error('获取仓库映射失败:', error)
    return warehouseMapCache // 返回缓存数据，即使可能过期
  }
}

// 报价单
export const getRemoteQuoteApply = async (params) => {
  const { pageNo, pageSize, query, ...restParams } = params;
  const response = await ApplyApi.getSimpleApplyPage({
    pageNo,
    pageSize,
    productName: query, // 映射到 API 的 `name` 字段
    ...restParams,
  });
  const { list, total } = await response;
  return { list, total };
}
